use crate::md_elem::elem::*;
use crate::md_elem::inline_regex_replace::{regex_replace_inlines, RegexReplaceError, Replaced};
use crate::md_elem::*;
use crate::output::inlines_to_plain_string;
use crate::select::{MatchR<PERSON><PERSON>, Matcher, SelectError};
use fancy_regex::Regex;
use std::borrow::{Borrow, Cow};

#[derive(Debug)]
pub(crate) struct StringMatcher {
    re: Regex,
    replacement: Option<String>,
}

#[derive(Clone, Debug)]
pub(crate) enum StringMatchError {
    NotSupported,
    RegexError(Box<fancy_regex::Error>),
    ReplaceError(RegexReplaceError),
}

#[must_use]
pub(crate) enum StringMatch<'a> {
    NoMatch(String),
    Match(String, Option<(&'a Regex, &'a str)>),
}

impl StringMatch<'_> {
    pub(crate) fn is_match(&self) -> bool {
        matches!(self, StringMatch::Match(..))
    }

    pub(crate) fn do_replace(self) -> String {
        match self {
            StringMatch::NoMatch(s) => s,
            StringMatch::Match(s, None) => s,
            StringMatch::Match(s, Some((re, replacement))) => re.replace_all(&s, replacement).to_string(),
        }
    }

    pub(crate) fn no_replace(self) -> String {
        match self {
            StringMatch::NoMatch(s) => s,
            StringMatch::Match(s, _) => s,
        }
    }
}

impl StringMatchError {
    pub(crate) fn to_select_error(&self, selector_name: &str) -> SelectError {
        let message = match self {
            StringMatchError::NotSupported => format!("{selector_name} selector does not support string replace"),
            StringMatchError::RegexError(err) => format!("regex evaluation error in {selector_name} selector: {err}"),
            StringMatchError::ReplaceError(err) => {
                format!("regex replacement error in {selector_name} selector: {err}")
            }
        };
        SelectError::new(message)
    }
}

impl PartialEq for StringMatcher {
    fn eq(&self, other: &Self) -> bool {
        self.re.as_str() == other.re.as_str() && self.replacement == other.replacement
    }
}

impl StringMatcher {
    pub(crate) fn matches(&self, haystack: &str) -> Result<bool, StringMatchError> {
        if self.replacement.is_some() {
            return Err(StringMatchError::NotSupported);
        }
        match self.re.is_match(haystack) {
            Ok(m) => Ok(m),
            Err(e) => Err(StringMatchError::RegexError(Box::new(e))),
        }
    }

    pub(crate) fn has_replacement(&self) -> bool {
        self.replacement.is_some()
    }

    pub(crate) fn match_replace(&self, haystack: String) -> Result<StringMatch, StringMatchError> {
        match self.re.is_match(&haystack) {
            Ok(is_match) => Ok(if is_match {
                let replacement = self.replacement.as_ref().map(|r| (&self.re, r.as_str()));
                StringMatch::Match(haystack, replacement)
            } else {
                StringMatch::NoMatch(haystack)
            }),
            Err(e) => Err(StringMatchError::RegexError(Box::new(e))),
        }
    }

    pub(crate) fn match_replace_string(&self, haystack: String) -> Result<Replaced<String>, StringMatchError> {
        let ok = match self.match_replace(haystack)? {
            StringMatch::NoMatch(orig) => Replaced {
                item: orig,
                matched_any: false,
            },
            StringMatch::Match(orig, None) => Replaced {
                item: orig,
                matched_any: true,
            },
            StringMatch::Match(orig, Some((pattern, replace_str))) => {
                let replaced_str = match pattern.replace_all(&orig, replace_str) {
                    Cow::Borrowed(_) => orig, // the borrowed value must be the orig, so no need to clone it
                    Cow::Owned(s) => s,
                };
                Replaced {
                    item: replaced_str,
                    matched_any: true,
                }
            }
        };
        Ok(ok)
    }

    pub(crate) fn match_replace_inlines(
        &self,
        haystack: Vec<Inline>,
    ) -> Result<Replaced<Vec<Inline>>, StringMatchError> {
        match &self.replacement {
            None => {
                let matched_any = self.matches_inlines(&haystack)?;
                let inlines = haystack;
                Ok(Replaced {
                    item: inlines,
                    matched_any,
                })
            }
            Some(replacement) => {
                let inline_replacements =
                    regex_replace_inlines(haystack, &self.re, &replacement).map_err(StringMatchError::ReplaceError)?;
                Ok(inline_replacements)
            }
        }
    }

    pub(crate) fn matches_inlines<I: Borrow<Inline>>(&self, haystack: &[I]) -> Result<bool, StringMatchError> {
        self.matches(&inlines_to_plain_string(haystack, Default::default()))
    }

    pub(crate) fn match_replace_any(
        &self,
        mut haystack: Vec<MdElem>,
    ) -> Result<Replaced<Vec<MdElem>>, StringMatchError> {
        let mut matched_any = false;

        for item in &mut haystack {
            let blank_elem = MdElem::Doc(Vec::new());
            let item_to_replace = std::mem::replace(item, blank_elem);
            let replaced = self.match_replace_node(item_to_replace)?;
            matched_any |= replaced.matched_any;
            let _ = std::mem::replace(item, replaced.item);
        }
        Ok(Replaced {
            item: haystack,
            matched_any,
        })
    }

    fn match_replace_node(&self, node: MdElem) -> Result<Replaced<MdElem>, StringMatchError> {
        match node {
            MdElem::Doc(elems) => {
                let replaced = self.match_replace_any(elems)?;
                Ok(Replaced {
                    item: MdElem::Doc(replaced.item),
                    matched_any: replaced.matched_any,
                })
            }
            MdElem::BlockQuote(block) => {
                let replaced = self.match_replace_any(block.body)?;
                Ok(Replaced {
                    item: MdElem::BlockQuote(BlockQuote { body: replaced.item }),
                    matched_any: replaced.matched_any,
                })
            }
            MdElem::List(mut list) => {
                let mut matched_any = false;
                for item in &mut list.items {
                    let contents = std::mem::take(&mut item.item);
                    let replaced = self.match_replace_any(contents)?;
                    matched_any |= replaced.matched_any;
                    item.item = replaced.item;
                }
                Ok(Replaced {
                    item: MdElem::List(list),
                    matched_any,
                })
            }
            MdElem::Section(section) => {
                let replaced_title = self.match_replace_inlines(section.title)?;
                let replaced_body = self.match_replace_any(section.body)?;
                Ok(Replaced {
                    item: MdElem::Section(Section {
                        title: replaced_title.item,
                        body: replaced_body.item,
                        depth: section.depth,
                    }),
                    matched_any: replaced_title.matched_any || replaced_body.matched_any,
                })
            }
            MdElem::Paragraph(p) => {
                let replaced = self.match_replace_inlines(p.body)?;
                Ok(Replaced {
                    item: MdElem::Paragraph(Paragraph { body: replaced.item }),
                    matched_any: replaced.matched_any,
                })
            }
            MdElem::Table(mut table) => {
                let mut matched_any = false;

                for row in &mut table.rows {
                    for cell in row {
                        let mut replaced = self.match_replace_inlines(std::mem::take(cell))?;
                        matched_any |= replaced.matched_any;
                        std::mem::swap(cell, &mut replaced.item);
                        // TODO -- optimization: if the replacement is None, we can break out of both loops early
                    }
                }
                Ok(Replaced {
                    item: MdElem::Table(table),
                    matched_any,
                })
            }
            MdElem::Inline(inline) => {
                let mut replaced = self.match_replace_inlines(vec![inline])?;
                let replaced_inline = replaced
                    .item
                    .pop()
                    .expect("while taking first element from replacement");
                assert!(
                    replaced.item.is_empty(),
                    "unexpected extra element(s) after replacing inlines: {:?}",
                    replaced.item
                );
                Ok(Replaced {
                    item: MdElem::Inline(replaced_inline),
                    matched_any: replaced.matched_any,
                })
            }
            MdElem::BlockHtml(html) => {
                let replaced = self.match_replace_string(html.value)?;
                Ok(Replaced {
                    item: MdElem::BlockHtml(BlockHtml { value: replaced.item }),
                    matched_any: replaced.matched_any,
                })
            }

            // Base cases: these don't recurse, so we say the StringMatcher doesn't match them. A Selector still may,
            // but that's Selector-specific logic, not StringMatcher logic.
            MdElem::ThematicBreak | MdElem::CodeBlock(_) | MdElem::FrontMatter(_) => Ok(Replaced {
                item: node,
                matched_any: false,
            }),
        }
    }

    fn re_for_any() -> Regex {
        Regex::new(".*").expect("internal error")
    }

    fn re_for_regex(re: Regex) -> Regex {
        re
    }

    fn re_from_text(text: String, case_sensitive: bool, anchor_start: bool, anchor_end: bool) -> Regex {
        let mut pattern = String::with_capacity(text.len() + 10); // +10 for modifiers, escapes, etc
        if !case_sensitive && !text.is_empty() {
            // (is_empty isn't necessary, just makes for a cleaner regex)
            pattern.push_str("(?i)");
        }
        if anchor_start {
            pattern.push('^');
        }
        pattern.push_str(&fancy_regex::escape(&text));
        if anchor_end {
            pattern.push('$');
        }

        Regex::new(&pattern).expect("internal error")
    }
}

impl From<MatchReplace> for StringMatcher {
    fn from(value: MatchReplace) -> Self {
        let MatchReplace { matcher, replacement } = value;
        let re = match matcher {
            Matcher::Text {
                case_sensitive,
                anchor_start,
                text,
                anchor_end,
            } => Self::re_from_text(text, case_sensitive, anchor_start, anchor_end),
            Matcher::Regex(re) => Self::re_for_regex(re.re),
            Matcher::Any { .. } => Self::re_for_any(),
        };
        Self { re, replacement }
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::query::{ParseError, StringVariant};
    use std::str::FromStr;

    /// PartialEq implementation for StringMatchError. This is only available in tests, so that we don't expose the
    /// brittle handling of our `RegexError` variant.
    impl PartialEq for StringMatchError {
        fn eq(&self, other: &Self) -> bool {
            match (self, other) {
                (StringMatchError::NotSupported, StringMatchError::NotSupported) => true,
                (StringMatchError::RegexError(_), StringMatchError::RegexError(_)) => {
                    // We can't compare fancy_regex::Error instances, so we'll consider them equal
                    // if they're both RegexError variants. This is sufficient for our testing needs.
                    true
                }
                _ => false,
            }
        }
    }

    #[test]
    fn bareword() {
        parse_and_check("hello", re_insensitive("hello"), "");
        parse_and_check("hello ", re_insensitive("hello"), "");
        parse_and_check("hello / goodbye", re_insensitive("hello / goodbye"), "");
        parse_and_check("hello| goodbye", re_insensitive("hello"), "| goodbye");
        parse_and_check("hello | goodbye", re_insensitive("hello"), "| goodbye");
        parse_and_check_with(
            StringVariant::AngleBracket,
            "foo> rest",
            re_insensitive("foo"),
            "> rest",
        );
    }

    #[test]
    fn bareword_anchor_start() {
        let m = parse_and_check("^ hello |after", re_insensitive("^hello"), "|after");
        assert!(!m.matches("pre hello").unwrap());
        assert!(m.matches("hello").unwrap());
        assert!(m.matches("hello post").unwrap());
    }

    #[test]
    fn bareword_anchor_end() {
        let m = parse_and_check(" hello $ |after", re_insensitive("hello$"), "|after");
        assert!(m.matches("pre hello").unwrap());
        assert!(m.matches("hello").unwrap());
        assert!(!m.matches("hello post").unwrap());
    }

    #[test]
    fn only_starting_anchor() {
        parse_and_check("^ |", StringMatcher::re_for_any(), "^ |");
        parse_and_check("^", StringMatcher::re_for_any(), "^");
    }

    #[test]
    fn only_ending_anchor() {
        parse_and_check("$ |", StringMatcher::re_for_any(), "$ |");
        parse_and_check("$", StringMatcher::re_for_any(), "$");
    }

    #[test]
    fn only_both_anchors() {
        let matcher = parse_and_check("^$ |after", re("^$"), "|after");
        assert!(matcher.matches("").unwrap());
        assert!(!matcher.matches("x").unwrap());
        assert!(!matcher.matches("\n").unwrap());

        parse_and_check("^  $ |after", re("^$"), "|after");
    }

    #[test]
    fn bareword_case_sensitivity() {
        let m = parse_and_check("hello", re_insensitive("hello"), "");
        assert!(m.matches("hello").unwrap());
        assert!(m.matches("HELLO").unwrap());
    }

    #[test]
    fn quoted_case_sensitivity() {
        let m = parse_and_check("'hello'", re("hello"), "");
        assert!(m.matches("hello").unwrap());
        assert!(!m.matches("HELLO").unwrap());
    }

    #[test]
    fn quoted_anchor_start() {
        let m = parse_and_check("^'hello'", re("^hello"), "");
        assert!(!m.matches("pre hello").unwrap());
        assert!(m.matches("hello").unwrap());
        assert!(m.matches("hello post").unwrap());
    }

    #[test]
    fn quoted_anchor_end() {
        let m = parse_and_check("'hello'$", re("hello$"), "");
        assert!(m.matches("pre hello").unwrap());
        assert!(m.matches("hello").unwrap());
        assert!(!m.matches("hello post").unwrap());
    }

    #[test]
    fn anchor_whitespace() {
        parse_and_check("^foo", re("(?i)^foo"), "");
        parse_and_check("^     foo", re("(?i)^foo"), "");
        parse_and_check("^   'foo'", re("^foo"), "");

        parse_and_check("bar$", re("(?i)bar$"), "");
        parse_and_check("bar     $", re("(?i)bar$"), "");
        parse_and_check("'bar'   $", re("bar$"), "");

        parse_and_check("^  foobar  $  ", re("(?i)^foobar$"), "");
    }

    #[test]
    fn bareword_regex_char() {
        let m = parse_and_check("hello.world", re_insensitive("hello\\.world"), "");
        assert!(m.matches("hello.world").unwrap());
        assert!(!m.matches("hello world").unwrap()); // the period is _not_ a regex any
    }

    #[test]
    fn bareword_end_delimiters() {
        parse_and_check_with(
            StringVariant::AngleBracket,
            "hello>world",
            re_insensitive("hello"),
            ">world",
        );

        // "$" is always an end delimiter
        parse_and_check_with(
            StringVariant::AngleBracket,
            "hello$world",
            re_insensitive("hello$"),
            "world", // note: the dollar sign got consumed, since it's part of the string
        );
    }

    /// Checks double-quoted string.
    ///
    /// Specifically:
    /// - single quotes can appear escaped or unescaped
    /// - double quotes must be escaped
    /// - \r, \n, \t
    /// - unicode code points
    #[test]
    fn double_quoted_string() {
        parse_and_check(
            r#" "hello world's ☃ \' \" \` \r \n \t says \"\u{2603}\" to me"_"#,
            re("hello world's ☃ ' \" ' \r \n \t says \"☃\" to me"),
            "_",
        );
    }

    /// Checks double-quoted string.
    ///
    /// See [double_quoted_string], except that _double_ quotes may be unescaped, and single quotes must be escaped.
    #[test]
    fn single_quoted_string() {
        parse_and_check(
            r#" 'hello world\'s ☃ \' \" \` \r \n \t says "\u{2603}" to me'_"#,
            re("hello world's ☃ ' \" ' \r \n \t says \"☃\" to me"),
            "_",
        );
    }

    #[test]
    fn quote_errs() {
        expect_empty(r#"" "#);
        expect_empty(r#"' "#);
        expect_empty(r#"'\"#);
        expect_empty(r#""\x" "#);
        expect_empty(r#""\u2603" "#);
        expect_empty(r#""\u{}" "#);
        expect_empty(r#""\u{12345678}" "#); // out of range
        expect_empty(r#""\u{snowman}" "#);
        expect_empty(r#""\u{2603"#);
    }

    //noinspection RegExpSingleCharAlternation (for the "(a|b)" case)
    #[test]
    fn regex() {
        parse_and_check(r#"/foo/"#, StringMatcher::re_for_regex(Regex::new("foo").unwrap()), "");
        parse_and_check(
            r#"/foo /"#,
            StringMatcher::re_for_regex(Regex::new("foo ").unwrap()),
            "",
        );
        parse_and_check(
            r#"/foo/bar"#,
            StringMatcher::re_for_regex(Regex::new("foo").unwrap()),
            "bar",
        );
        parse_and_check(r#"//"#, StringMatcher::re_for_regex(Regex::new("").unwrap()), "");
        parse_and_check(
            r#"/(a|b)/"#,
            StringMatcher::re_for_regex(Regex::new("(a|b)").unwrap()),
            "",
        );
        parse_and_check(r#"/\d/"#, StringMatcher::re_for_regex(Regex::new("\\d").unwrap()), "");
        parse_and_check(
            r#"/fizz\/buzz/"#,
            StringMatcher::re_for_regex(Regex::new("fizz/buzz").unwrap()),
            "",
        );

        expect_empty(r#"/unclosed"#);

        expect_err(r#"/(unclosed paren/"#);
    }

    #[test]
    fn any() {
        let empty_matcher = parse_and_check("| rest", StringMatcher::re_for_any(), "| rest");
        assert!(empty_matcher.matches("").unwrap());

        parse_and_check("| rest", StringMatcher::re_for_any(), "| rest");
        parse_and_check("*| rest", StringMatcher::re_for_any(), "| rest");
        parse_and_check("* | rest", StringMatcher::re_for_any(), "| rest");
        parse_and_check_with(
            StringVariant::AngleBracket,
            "> rest",
            StringMatcher::re_for_any(),
            "> rest",
        );
    }

    /// Test for fancy_regex specific feature (lookaround)
    #[test]
    fn fancy_regex_lookahead() {
        let re_instance = re(r#"foo(?=bar)"#); // Positive lookahead: matches "foo" only if followed by "bar"
        let matcher = StringMatcher {
            re: re_instance,
            replacement: None,
        };
        assert!(matcher.matches("foobar").unwrap());
        assert!(!matcher.matches("foo").unwrap());
        assert!(!matcher.matches("foobaz").unwrap());
    }

    #[test]
    fn matches_with_replacement_returns_not_supported_error() {
        let matcher_with_replacement = StringMatcher::from(MatchReplace {
            matcher: Matcher::Text {
                case_sensitive: false,
                anchor_start: false,
                text: "hello".to_string(),
                anchor_end: false,
            },
            replacement: Some("world".to_string()),
        });

        assert_eq!(
            matcher_with_replacement.matches("hello"),
            Err(StringMatchError::NotSupported)
        );
    }

    #[test]
    fn matches_inlines_with_replacement_returns_not_supported_error() {
        let matcher_with_replacement = StringMatcher::from(MatchReplace {
            matcher: Matcher::Any { explicit: false },
            replacement: Some("replacement".to_string()),
        });

        let inlines: Vec<Inline> = vec![]; // empty inlines for simplicity
        assert_eq!(
            matcher_with_replacement.matches_inlines(&inlines),
            Err(StringMatchError::NotSupported)
        );
    }

    #[test]
    fn string_match_error_to_select_error_formatting() {
        let not_supported_error = StringMatchError::NotSupported;
        let select_error = not_supported_error.to_select_error("section");
        assert_eq!(
            select_error.to_string(),
            "section selector does not support string replace"
        );
    }

    #[test]
    fn match_replace_matches_with_replacement() {
        let matcher_with_replacement = StringMatcher::from(MatchReplace {
            matcher: Matcher::Text {
                case_sensitive: false,
                anchor_start: false,
                text: "hello".to_string(),
                anchor_end: false,
            },
            replacement: Some("world".to_string()),
        });

        let haystack = "hello there".to_string();
        let result = matcher_with_replacement.match_replace(haystack).unwrap();
        assert!(result.is_match());
        assert_eq!(result.do_replace(), "world there");
    }

    #[test]
    fn match_replace_matches_without_replacement() {
        let matcher_with_replacement = StringMatcher::from(MatchReplace {
            matcher: Matcher::Text {
                case_sensitive: false,
                anchor_start: false,
                text: "hello".to_string(),
                anchor_end: false,
            },
            replacement: Some("world".to_string()),
        });

        let haystack = "hello there".to_string();
        let result = matcher_with_replacement.match_replace(haystack).unwrap();
        assert!(result.is_match());
        assert_eq!(result.no_replace(), "hello there");
    }

    #[test]
    fn match_replace_without_replacement() {
        let matcher_without_replacement = StringMatcher::from(MatchReplace {
            matcher: Matcher::Text {
                case_sensitive: false,
                anchor_start: false,
                text: "hello".to_string(),
                anchor_end: false,
            },
            replacement: None,
        });

        let haystack = "hello there".to_string();
        let result = matcher_without_replacement.match_replace(haystack).unwrap();
        assert!(result.is_match());
        assert_eq!(result.no_replace(), "hello there"); // unchanged
    }

    #[test]
    fn match_replace_no_match() {
        let matcher = StringMatcher::from(MatchReplace {
            matcher: Matcher::Text {
                case_sensitive: false,
                anchor_start: false,
                text: "goodbye".to_string(),
                anchor_end: false,
            },
            replacement: Some("world".to_string()),
        });

        let haystack = "hello there".to_string();
        let result = matcher.match_replace(haystack).unwrap();
        assert!(!result.is_match());
        assert_eq!(result.no_replace(), "hello there"); // unchanged
    }

    fn parse_and_check_with(
        string_variant: StringVariant,
        text: &str,
        expect_re: Regex,
        expect_remaining: &str,
    ) -> StringMatcher {
        let (actual_matcher, actual_remaining) = match Matcher::parse(string_variant, text) {
            Ok(parsed) => parsed,
            Err(err) => {
                let public_err = ParseError::new(err);
                panic!("{public_err:?}")
            }
        };
        let expect = StringMatcher {
            re: expect_re,
            replacement: None,
        };
        let actual_string_matcher: StringMatcher = actual_matcher.into();
        assert_eq!(actual_string_matcher, expect);
        assert_eq!(actual_remaining, expect_remaining);
        expect
    }

    fn parse_and_check(text: &str, expect: Regex, expect_remaining: &str) -> StringMatcher {
        parse_and_check_with(StringVariant::Pipe, text, expect, expect_remaining)
    }

    fn expect_empty(text: &str) {
        parse_and_check(text, StringMatcher::re_for_any(), text);
    }

    fn expect_err(text: &str) {
        if let Ok(unexpected) = Matcher::parse(StringVariant::Pipe, text) {
            panic!("unexpected success: {unexpected:?}")
        }
    }

    fn re(value: &str) -> Regex {
        Regex::new(value).expect("test error")
    }

    fn re_insensitive(value: &str) -> Regex {
        let mut s = String::with_capacity(value.len() + 3);
        s.push_str("(?i)");
        s.push_str(value);
        re(&s)
    }

    impl From<&str> for StringMatcher {
        fn from(value: &str) -> Self {
            Self {
                re: Regex::from_str(value).unwrap(),
                replacement: None,
            }
        }
    }
}
